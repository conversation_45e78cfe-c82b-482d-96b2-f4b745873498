name: Bug Report
description: File a bug report
title: "[Bug]: "
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  
  - type: textarea
    id: bug-description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: When I click on X, the app crashes...
    validations:
      required: true
      
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
      
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: I expected X to happen...
    validations:
      required: true
      
  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual Behavior
      description: What actually happened instead.
      placeholder: Instead, Y happened...
    validations:
      required: true
      
  - type: dropdown
    id: browsers
    attributes:
      label: Browser
      description: What browser are you seeing the problem on?
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Microsoft Edge
        - Other
    validations:
      required: false
      
  - type: dropdown
    id: device
    attributes:
      label: Device
      description: What device are you using?
      options:
        - Desktop
        - Mobile
        - Tablet
    validations:
      required: false
      
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here (screenshots, error messages, etc.)
    validations:
      required: false