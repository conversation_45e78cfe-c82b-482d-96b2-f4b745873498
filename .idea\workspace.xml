<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="60f83504-9481-42d8-84eb-2f9a05dc4536" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;0Xsolcreator&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/TheSolaAI/sola-application.git&quot;,
    &quot;accountId&quot;: &quot;6ba7d2f3-7a83-42ee-af2e-1ba1596c809e&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2u4IHpgPiZfuZi66BOcLoPqri0N" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;/home/<USER>/Sola/sola-application/node_modules/prettier&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/home/<USER>/Sola/sola-application/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-WS-243.24978.60" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="60f83504-9481-42d8-84eb-2f9a05dc4536" name="Changes" comment="" />
      <created>1741501335093</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741501335093</updated>
      <workItem from="1741501336566" duration="1378000" />
      <workItem from="1741502749445" duration="13729000" />
      <workItem from="1741584828449" duration="5314000" />
      <workItem from="1741670844655" duration="2989000" />
      <workItem from="1741755408478" duration="19585000" />
      <workItem from="1741845055346" duration="5224000" />
      <workItem from="1741859890252" duration="13000" />
      <workItem from="1741883355028" duration="638000" />
      <workItem from="1741927225310" duration="22000" />
      <workItem from="1742275227622" duration="4518000" />
      <workItem from="1742295885232" duration="15000" />
      <workItem from="1742400789599" duration="60000" />
      <workItem from="1742486536081" duration="1275000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>