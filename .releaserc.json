{"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "releaseRules": [{"type": "bump", "release": "minor"}, {"type": "feat", "release": "patch"}, {"type": "fix", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "style", "release": "patch"}, {"type": "refactor", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "test", "release": "patch"}, {"type": "build", "release": "patch"}, {"type": "ci", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "revert", "release": "patch"}, {"type": "patch", "release": "patch"}, {"type": "deps", "release": "patch"}], "parserOpts": {"headerPattern": "^(\\w+):\\s(:\\w+:)\\s(.+?)(?:\\s\\(#\\d+\\))?$", "headerCorrespondence": ["type", "<PERSON><PERSON><PERSON><PERSON>", "subject"], "mergePattern": "^Merge pull request #(\\d+) from (.*)$", "mergeCorrespondence": ["id", "source"]}}], "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/npm", {"npmPublish": false}], ["semantic-release-github-pullrequest", {"assets": ["package.json", "CHANGELOG.md"], "pullrequestTitle": "chore: :bookmark: Release ${nextRelease.version}", "pullrequestDescription": "${nextRelease.notes}", "branch": "release/${nextRelease.version}"}], "@semantic-release/github"]}