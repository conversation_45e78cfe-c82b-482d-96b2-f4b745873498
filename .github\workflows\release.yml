name: Release

on:
  push:
    branches:
      - main

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
      pull-requests: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN }}
          GH_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN }}
          GH_TOKEN_RELEASE: ${{ secrets.SEMANTIC_RELEASE_TOKEN }}
          GIT_AUTHOR_NAME: github-actions
          GIT_AUTHOR_EMAIL: <EMAIL>
          GIT_COMMITTER_NAME: github-actions
          GIT_COMMITTER_EMAIL: <EMAIL>
        run: yarn semantic-release
