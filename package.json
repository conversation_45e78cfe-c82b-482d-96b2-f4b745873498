{"name": "sola-application", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "prepare": "husky install", "semantic-release": "semantic-release"}, "dependencies": {"@ai-sdk/openai": "^1.3.0", "@ai-sdk/react": "^1.2.9", "@bonfida/spl-name-service": "^3.0.10", "@headlessui/react": "^2.2.0", "@microsoft/clarity": "^1.0.0", "@phantom/wallet-sdk": "^0.0.23", "@prisma/client": "^6.5.0", "@privy-io/react-auth": "^2.6.4", "@privy-io/server-auth": "^1.19.0", "@reduxjs/toolkit": "^2.7.0", "@sentry/nextjs": "^9", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "ai": "^4.2.0", "axios": "^1.8.4", "axios-retry": "^4.5.0", "framer-motion": "^12.4.10", "next": "15.3.2", "openai": "^4.95.1", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-spinners": "^0.15.0", "react-syntax-highlighter": "^15.6.1", "recharts": "3.0.0-alpha.9", "redux-persist": "^6.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3", "@sola-labs/ai-kit": "0.1.9"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@tailwindcss/postcss": "^4", "@types/bs58": "^6.0.0.", "@types/node": "^20", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.27.0", "autoprefixer": "^10.4.20", "commitlint": "^19.8.0", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prisma": "^6.6.0", "semantic-release": "^24.2.3", "semantic-release-github-pullrequest": "^1.3.0", "tailwindcss": "3", "typescript": "^5"}, "resolutions": {"@types/react": "19.1.5", "@types/react-dom": "19.1.5"}}